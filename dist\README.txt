MIGRACE OBJEDNÁVEK - NÁVOD K POUŽITÍ
=====================================

POPIS:
------
Tento program slouží k migraci objednávek z formátů LABARA a DESCON do standardizovaného CSV formátu.
Program je kompletně samostat<PERSON>ý (standalone) a nevyžaduje instalaci Pythonu ani dalších zá<PERSON>lostí.

POŽADAVKY:
----------
- Windows 10/11
- Katalogový soubor "ALDE_katalog_položek.xlsx" ve stejné složce jako EXE soubor

POUŽITÍ:
--------
1. <PERSON>ís<PERSON>ěte soubor "Migrace_Objednavek.exe" do složky s katalogem "ALDE_katalog_položek.xlsx"
2. <PERSON>pus<PERSON><PERSON> "Migrace_Objednavek.exe" dvojklikem
3. Vyberte typ objednávky:
   - LABARA: Pro zpracování CSV souborů objednávek LABARA
   - DESCON: Pro zpracování Excel souborů objednávek DESCON

ZPRACOVÁNÍ LABARA:
------------------
1. Klikněte na "Zpracovat objednávku LABARA"
2. Vyberte CSV soubor s objednávkou
3. Zadejte datum dodání (formát DD.MM.RRRR)
4. Zadejte číslo zákazníka
5. Program vytvoří výstupní CSV soubor ve složce "vystup"

ZPRACOVÁNÍ DESCON:
------------------
1. Klikněte na "Zpracovat objednávku DESCON"
2. Vyberte Excel soubor s objednávkou
3. Program automaticky zpracuje data a vytvoří výstupní CSV soubor ve složce "vystup"

VÝSTUPNÍ SOUBORY:
-----------------
- Výstupní soubory jsou uloženy ve složce "vystup" (vytvoří se automaticky)
- Formát: CSV s oddělovačem středník (;)
- Kódování: UTF-8 s BOM (kompatibilní s Excelem)
- Soubory lze otevřít v Excelu nebo importovat do dalších systémů

STRUKTURA VÝSTUPNÍHO SOUBORU:
-----------------------------
CONTRACT;CUSTOMER_NO;CATALOG_NO;QTY;DATED;PRICE;CURRENCY_CODE;CUSTOMER_PO_NO;W;L

ŘEŠENÍ PROBLÉMŮ:
----------------
- Pokud program nefunguje, zkontrolujte, zda je katalogový soubor "ALDE_katalog_položek.xlsx" ve stejné složce
- Pro položky bez shody v katalogu se zobrazí upozornění
- Výstupní soubory neobsahují položky, pro které nebyla nalezena shoda v katalogu

KONTAKT:
--------
V případě problémů kontaktujte správce systému.

Verze: 1.0
Datum: 2025-06-30
