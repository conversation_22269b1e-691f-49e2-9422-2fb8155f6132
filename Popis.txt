Popis a funkce programu: Nástroj pro migraci objednávek
Hlavní účel
Tento program automatizuje převod objednávek od zákazníků (konkrétně Labara a Descon) do jednotného CSV formátu. Cílem je připravit data pro snadný import do interního informačního systému, č<PERSON><PERSON>ž se šetří čas a minimalizují chyby z ručního přepisu.
Klíčové funkce a procesy
Program je rozdělen na dvě hlavní, nezávislé části podle typu zákazníka:
1. Zpracování objednávky LABARA
Tato část je navržena pro zpracování CSV souborů od zákazníka Labara, které obsahují unikátní kódy zboží.
Párování položek: Program využívá nejspolehlivější metodu párování. Pro každou položku v objednávce vezme jedinečný kód ze sloupce číslo zboží a hledá jeho přesnou shodu v interním katalogu dílů (ALDE_katalog_položek.xlsx). Tím je zajištěno, že každé položce je přiřazen správný interní kód (CATALOG_NO). Tento proces vyžaduje, aby katalog obsahoval "překladový" sloupec s kódy zákazníka.
Extrakce rozměrů: Z textového popisu položky (název) program automaticky "vytáhne" číselné hodnoty pro šířku (W) a délku (L).
Doplnění chybějících údajů: Jelikož CSV soubor neobsahuje všechny potřebné informace, program si na začátku vyžádá od uživatele doplnění datumu dodání a čísla zákazníka prostřednictvím dialogového okna.
2. Zpracování objednávky DESCON
Tato část je určena pro zpracování detailních Excel souborů od zákazníka Descon.
Párování položek: Zde program používá "chytrou" metodu párování. Z několika různých sloupců v objednávce (jako MaterialVyroba, TYP, Tloustka) si dynamicky sestaví unikátní "vyhledávací klíč".
Vyhledávání v katalogu: Tento sestavený klíč pak porovnává s popisy v interním katalogu (ALDE_katalog_položek.xlsx), aby nalezl odpovídající interní kód dílu (CATALOG_NO).
Sběr dat: Veškeré potřebné informace, včetně rozměrů (W, L) a data dodání, si program načítá přímo z různých buněk a sloupců v Excel souboru.
Výsledný produkt
Pro oba typy objednávek je výsledkem standardizovaný CSV soubor uložený ve složce vystup. Tento soubor má vždy stejnou strukturu a pořadí sloupců, takže je připraven pro bezproblémovou migraci do cílového systému.